/**
 * The app navigator (formerly "AppNavigator" and "MainNavigator") is used for the primary
 * navigation flows of your app.
 * Generally speaking, it will contain an auth flow (registration, login, forgot password)
 * and a "main" flow which the user will use once logged in.
 */

import { NavigationContainer } from "@react-navigation/native"
import { createNativeStackNavigator, NativeStackScreenProps } from "@react-navigation/native-stack"
import { observer } from "mobx-react-lite"
import * as Screens from "@/screens"
import Config from "../config"
import { useStores } from "../models"
import { navigationRef, useBackButtonHandler } from "./navigationUtilities"
import { useAppTheme, useThemeProvider } from "@/utils/useAppTheme"
import React, { ComponentProps } from "react"
import { CommunityDetailScreen } from "@/screens/CommunityDetail/CommunityDetailScreen"
import { NotificationsScreen } from "@/screens/Notifications"
import { MainScreen } from "@/screens/Main"
import { EventsScreen } from "@/screens/Events"
import { CommunitiesScreen } from "@/screens/Communities"
import { ProfileScreen } from "@/screens/Profile"
import { EventDetailScreen } from "@/screens/EventDetail"
import { EditProfileScreen } from "@/screens"
import { Platform } from "react-native"
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet"
import { EventResult } from "@/screens/EventResult"

/**
 * This type allows TypeScript to know what routes are defined in this navigator
 * as well as what properties (if any) they might take when navigating to them.
 *
 * If no params are allowed, pass through `undefined`. Generally speaking, we
 * recommend using your MobX-State-Tree store(s) to keep application state
 * rather than passing state through navigation params.
 *
 * For more information, see this documentation:
 *   https://reactnavigation.org/docs/params/
 *   https://reactnavigation.org/docs/typescript#type-checking-the-navigator
 *   https://reactnavigation.org/docs/typescript/#organizing-types
 */
export type AppStackParamList = {
  Login: undefined
  SignUp: undefined
  EmailVerification: { email: string; password: string; displayName: string }
  ResetPassword: undefined
  PasswordResetConfirm: undefined
  Main: undefined
  Events: undefined
  Communities: undefined
  Profile: undefined
  CommunityDetail: { communityId: string }
  Notifications: undefined
  Inbox: undefined
  EventDetail: { eventId: number }
  EventResult: { eventId: number }
  MemberProfile: { userId: string }
  PrivateChat: { participantId: string; participantName: string }
  EditProfile: { initialProfile?: Partial<Omit<import("@/screens/EditProfile/types").UserProfile, 'dateOfBirth'> & { dateOfBirth: string | Date }> }
  ChangePassword: undefined
  CreateCommunity: { mode?: 'create' | 'edit'; initialData?: Partial<import("@/screens/CreateCommunity/hooks").CreateCommunityData> }
  CreateEvent: undefined
  // 🔥 Your screens go here
  // IGNITE_GENERATOR_ANCHOR_APP_STACK_PARAM_LIST
}

/**
 * This is a list of all the route names that will exit the app if the back button
 * is pressed while in that screen. Only affects Android.
 */
const exitRoutes = Config.exitRoutes

export type AppStackScreenProps<T extends keyof AppStackParamList> = NativeStackScreenProps<
  AppStackParamList,
  T
>

// Documentation: https://reactnavigation.org/docs/stack-navigator/
const Stack = createNativeStackNavigator<AppStackParamList>()

const AppStack = observer(function AppStack() {
  const {
    authenticationStore: { isAuthenticated },
  } = useStores()

  const {
    theme: { colors },
  } = useAppTheme()



  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        navigationBarColor: colors.background,
        contentStyle: {
          backgroundColor: colors.background,
        },
      }}
      initialRouteName={isAuthenticated ? "Main" : "Login"}
    >
      {isAuthenticated ? (
        <>
          <Stack.Screen name="Main" component={MainScreen} />

          <Stack.Screen name="Events" component={EventsScreen} />

          <Stack.Screen name="Communities" component={CommunitiesScreen} />

          <Stack.Screen name="Profile">{(props) => <ProfileScreen {...props} />}</Stack.Screen>

          <Stack.Screen name="CommunityDetail" component={CommunityDetailScreen} />

          <Stack.Screen 
            name="Notifications" 
            component={NotificationsScreen}
            options={{
              headerShown: false,
              presentation: Platform.OS === "ios" ? "modal" : undefined,
              animation: Platform.OS === "ios" ? "slide_from_bottom" : "none",
              contentStyle: {
                backgroundColor: "transparent",
              },
            }}
          />

          <Stack.Screen name="EventDetail" component={EventDetailScreen} />

          <Stack.Screen name="EventResult" component={EventResult} />

          <Stack.Screen name="MemberProfile">
            {(props) => <Screens.ProfileScreen {...props} />}
          </Stack.Screen>

          <Stack.Screen name="PrivateChat" component={Screens.PrivateChatScreen} />

          {/* EditProfile Screen */}
          <Stack.Screen
            name="EditProfile"
            component={EditProfileScreen}
            options={{
              headerShown: false,
            }}
          />

          {/* Change Password Screen */}
          <Stack.Screen
            name="ChangePassword"
            component={Screens.ChangePasswordScreen}
            options={{
              headerShown: false,
            }}
          />
      
          {/* Create Community Screen */}
          <Stack.Screen
            name="CreateCommunity"
            component={Screens.CreateCommunityScreen}
            options={{
              headerShown: false,
              presentation: Platform.OS === "ios" ? "modal" : undefined,
              animation: Platform.OS === "ios" ? "slide_from_bottom" : "none",
              contentStyle: {
                backgroundColor: "transparent",
              },
            }}
          />

          <Stack.Screen
            name="CreateEvent"
            component={Screens.CreateEventScreen}
            options={{
              headerShown: false,
              presentation: Platform.OS === "ios" ? "modal" : undefined,
              animation: Platform.OS === "ios" ? "slide_from_bottom" : "none",
              contentStyle: {
                backgroundColor: "transparent",
              },
            }}
          />

          {/* Inbox Screen */}
          <Stack.Screen name="Inbox" component={Screens.InboxScreen} />

          {/* Password Reset Confirm - Available in authenticated stack for deep link navigation */}
          <Stack.Screen name="PasswordResetConfirm" component={Screens.PasswordResetConfirmScreen} />
        </>
      ) : (
        <>
          <Stack.Screen name="Login" component={Screens.LoginScreen} />
          <Stack.Screen name="SignUp" component={Screens.SignUpScreen} />
          <Stack.Screen name="ResetPassword" component={Screens.ResetPasswordScreen} />
          <Stack.Screen name="PasswordResetConfirm" component={Screens.PasswordResetConfirmScreen} />
          <Stack.Screen name="EmailVerification" component={Screens.EmailVerificationScreen} />
        </>
      )}

      {/** 🔥 Your screens go here */}
      {/* IGNITE_GENERATOR_ANCHOR_APP_STACK_SCREENS */}
    </Stack.Navigator>
  )
})

export interface NavigationProps
  extends Partial<ComponentProps<typeof NavigationContainer<AppStackParamList>>> {}

export const AppNavigator = observer(function AppNavigator(props: NavigationProps) {
  const { themeScheme, navigationTheme, setThemeContextOverride, ThemeProvider } =
    useThemeProvider()

  useBackButtonHandler((routeName) => exitRoutes.includes(routeName))

  return (
    <ThemeProvider value={{ themeScheme, setThemeContextOverride }}>
      <NavigationContainer ref={navigationRef} theme={navigationTheme} {...props}>
        <BottomSheetModalProvider>
          <Screens.ErrorBoundary catchErrors={Config.catchErrors}>
            <AppStack />
          </Screens.ErrorBoundary>
        </BottomSheetModalProvider>
      </NavigationContainer>
    </ThemeProvider>
  )
})
