import { Instance, SnapshotOut, types } from "mobx-state-tree"
import { Trophy, UserCommunity } from "@/screens/Profile/types"

const TrophyModel = types.model("Trophy", {
  id: types.identifier,
  title: types.string,
  description: types.string,
  date: types.string,
  icon: types.string,
})

export const UserProfileStoreModel = types
  .model("UserProfileStore", {
    userId: types.maybe(types.string),
    displayName: "",
    email: "",
    countryCode: types.maybe(types.string),
    phone: types.maybe(types.string),
    gender: types.maybe(types.string),
    dateOfBirth: types.maybe(types.Date),
    description: types.maybe(types.string),
    location: types.maybe(types.string),
    /**
     * Cached statistics for the authenticated user. These map 1-to-1 with the
     * columns in `public.user_stats`.
     */
    totalMatches: types.optional(types.number, 0),
    socialWins: types.optional(types.number, 0),
    tournamentWins: types.optional(types.number, 0),
    trophies: types.optional(types.array(TrophyModel), []),
    communities: types.optional(types.array(types.frozen<UserCommunity>()), []), // Using frozen as UserCommunity is complex
  })
  .views((store) => ({
    /**
     * Check if all mandatory profile fields are filled.
     * Mandatory fields for new users: displayName, gender, dateOfBirth, location
     */
    get hasMandatoryFields() {
      return !!(
        store.displayName &&
        store.displayName.trim() !== "" &&
        store.gender &&
        store.dateOfBirth &&
        store.location
      )
    },
  }))
  .actions((store) => ({
    setProfile(data: Partial<typeof store>) {
      Object.assign(store, data)
    },
    clear() {
      store.userId = undefined
      store.displayName = ""
      store.email = ""
      store.countryCode = undefined
      store.phone = undefined
      store.gender = undefined
      store.dateOfBirth = undefined
      store.description = undefined
      store.location = undefined
      // Reset cached stats
      store.totalMatches = 0
      store.socialWins = 0
      store.tournamentWins = 0
      store.trophies.clear()
      store.communities.clear()
    },
    setStats(data: {
      totalMatches?: number
      socialWins?: number
      tournamentWins?: number
    }) {
      if (typeof data.totalMatches === "number") store.totalMatches = data.totalMatches
      if (typeof data.socialWins === "number") store.socialWins = data.socialWins
      if (typeof data.tournamentWins === "number") store.tournamentWins = data.tournamentWins
    },
    setTrophies(trophies: Trophy[]) {
      store.trophies.replace(trophies as any)
    },
    setCommunities(communities: UserCommunity[]) {
      store.communities.replace(communities as any)
    },
  }))

export interface UserProfileStore extends Instance<typeof UserProfileStoreModel> {}
export interface UserProfileStoreSnapshot extends SnapshotOut<typeof UserProfileStoreModel> {}
