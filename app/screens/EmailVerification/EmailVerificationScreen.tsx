import { observer } from "mobx-react-lite"
import { FC, useCallback, useState } from "react"
import { ViewStyle, TextStyle, View } from "react-native"
import { Button, Screen, Text } from "@/components"
import { OTPInput } from "@/components/OTPInput"
import { useAppTheme } from "@/utils/useAppTheme"
import { SupabaseAuthService } from "@/services/supabase/auth-service"
import { SupabaseProfileService } from "@/services/supabase/profile-service"
import { useStores } from "@/models"
import type { EmailVerificationScreenProps } from "./types"

export const EmailVerificationScreen: FC<EmailVerificationScreenProps> = observer(function EmailVerificationScreen({ route, navigation }) {
  const { email, password, displayName } = route.params
  const [code, setCode] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const { themed } = useAppTheme()
  const {
    authenticationStore: { setAuthToken, setCurrentUserId },
    userProfileStore
  } = useStores()

  const handleVerify = useCallback(async () => {
    // code length check will be handled by disabled state
    try {
      setIsLoading(true)
      const { session, user } = await SupabaseAuthService.verifyEmailOtp(email, code)
      if (session?.access_token && user) {
        // set password now
        await SupabaseAuthService.updateUserPassword(password)
        if (displayName) {
          await SupabaseAuthService.updateUserProfile({ display_name: displayName })
        }
        setAuthToken(session.access_token)
        setCurrentUserId(user.id)

        // Fetch profile row and cache (similar to login flow)
        SupabaseProfileService.fetchCurrentUserProfile()
          .then((profile) => {
            if (profile) {
              userProfileStore.setProfile({
                userId: user.id,
                displayName: profile.name,
                email: profile.email,
                countryCode: profile.countryCode,
                phone: profile.phone,
                gender: profile.gender,
                dateOfBirth: profile.dateOfBirth,
                description: profile.description,
                location: profile.location
              })
            }
          })
          .catch(console.error)

        navigation.reset({ index: 0, routes: [{ name: "Main" }] })
      }
    } catch (e) {
      console.error("OTP verification failed", e)
    } finally {
      setIsLoading(false)
    }
  }, [code, email, password, displayName])

  return (
    <Screen
      preset="auto"
      contentContainerStyle={themed($container)}
      safeAreaEdges={["top", "bottom"]}
    >
      <Text text="Enter the 6-digit code" preset="heading" style={themed($heading)} />
      <Text text={`We've sent it to ${email}`} size="sm" style={themed($subheading)} />

      <OTPInput value={code} onChange={setCode} autoFocus length={6} />

      <View style={{ height: 24 }} />

      <Button
        text="Verify"
        onPress={handleVerify}
        disabled={isLoading || code.length !== 6}
        preset="reversed"
      />

      <Text
        text="Resend code"
        size="sm"
        weight="light"
        style={{ marginTop: 16, textAlign: "center" }}
        onPress={() => SupabaseAuthService.requestEmailOtp(email)}
      />
    </Screen>
  )
})

const $container: ViewStyle = {
  paddingHorizontal: 20,
  paddingTop: 40,
}

const $heading: TextStyle = {
  marginBottom: 4,
}

const $subheading: TextStyle = {
  marginBottom: 24,
}

const $codeField: ViewStyle = {
  marginBottom: 24,
} 