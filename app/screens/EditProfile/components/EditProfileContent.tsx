import { FC } from "react"
import {
  ViewStyle,
  ScrollView,
  KeyboardAvoidingView,
  Platform
} from "react-native"
import { observer } from "mobx-react-lite"
import { Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { getSafeAreaConfig } from "@/utils/safeAreaHelpers"
import { $styles } from "@/theme"
import type { ThemedStyle } from "@/theme"
import type { EditProfileContentProps } from "../types"
import { ProfileAvatar } from "./ProfileAvatar"
import { FormFields } from "./FormFields"

export const EditProfileContent: FC<EditProfileContentProps> = observer(function EditProfileContent({
  formData,
  errors,
  onFieldChange,
  loading = false,
}) {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()
  const $bottomInset = useSafeAreaInsetsStyle(["bottom"])

  // Actions management hook - simplified since we only need image picker
  const handleImagePicker = () => {
    // TODO: Implement image picker functionality
    console.log("Image picker not implemented yet")
  }

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={getSafeAreaConfig("CONTENT_ONLY")}
      contentContainerStyle={$styles.flex1}
      statusBarStyle="light"
      backgroundColor={colors.background}
    >
      <KeyboardAvoidingView
        style={themed($keyboardContainer)}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 100 : 80}
      >
        {/* Content */}
        <ScrollView
          style={themed($scrollView)}
          contentContainerStyle={[themed($scrollContent), $bottomInset]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Profile Avatar Section */}
          <ProfileAvatar
            name={formData.name}
            avatar={formData.avatar}
            onImagePress={handleImagePicker}
          />

          {/* Form Fields Section */}
          <FormFields
            formData={formData}
            errors={errors}
            onFieldChange={onFieldChange}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </Screen>
  )
})

const $keyboardContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $scrollView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $scrollContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingTop: spacing.lg,
  paddingBottom: spacing.xl,
  // Remove horizontal padding here since FormFields handles it
})