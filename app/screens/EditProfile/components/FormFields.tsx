import { FC, useState } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, Platform, Keyboard } from "react-native"
import { observer } from "mobx-react-lite"
import { TextField, Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { EditProfileFormData } from "../types"
import { COUNTRY_CODES, GENDER_OPTIONS, formatDateForDisplay } from "../utils"
import { translate } from "@/i18n"
import { SelectionSheet } from "./SelectionSheet"
import { DatePickerModal } from "./DatePickerModal"
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker"

interface FormFieldsProps {
  formData: EditProfileFormData
  errors: Record<string, string>
  onFieldChange: (field: keyof EditProfileFormData, value: string | Date) => void
}

export const FormFields: FC<FormFieldsProps> = observer(function FormFields({
  formData,
  errors,
  onFieldChange,
}) {
  const { themed } = useAppTheme()
  
  // Helper to normalize dates to midnight in the device's locale
  const normalizeDate = (date: Date) =>
    new Date(date.getFullYear(), date.getMonth(), date.getDate())

  // Modal visibility states
  const [showCountryModal, setShowCountryModal] = useState(false)
  const [showGenderModal, setShowGenderModal] = useState(false)
  const [showDatePicker, setShowDatePicker] = useState(false)

  // Find current selections for display
  const currentCountry = COUNTRY_CODES.find(c => c.code === formData.countryCode)
  const currentGender = GENDER_OPTIONS.find(g => g.value === formData.gender)

  // Only show the numeric part of country code (e.g., +971)
  const displayCountryCode = currentCountry
    ? (currentCountry.code.split(" ")[1] ?? currentCountry.code)
    : (formData.countryCode.split(" ")[1] ?? formData.countryCode)

  const handleDateSelect = (selectedDate: Date) => {
    onFieldChange("dateOfBirth", normalizeDate(selectedDate))
    setShowDatePicker(false)
  }

  const handleOpenDatePicker = () => {
    Keyboard.dismiss()
    if (Platform.OS === "android") {
      DateTimePickerAndroid.open({
        value: formData.dateOfBirth,
        mode: "date",
        maximumDate: new Date(),
        minimumDate: new Date(1900, 0, 1),
        onChange: (_event, date) => {
          if (date) handleDateSelect(date)
        },
      })
    } else {
      setShowDatePicker(true)
    }
  }

  return (
    <View style={themed($container)}>
      <Text tx="editProfileScreen:personalInformationTitle" style={themed($sectionTitle)} size="lg" weight="semiBold" />

      {/* Name Field */}
      <TextField
        labelTx="editProfileScreen:nameAndSurnameLabel"
        value={formData.name}
        onChangeText={(text) => onFieldChange("name", text)}
        placeholderTx="editProfileScreen:enterYourNamePlaceholder"
        helper={errors.name}
        status={errors.name ? "error" : undefined}
        containerStyle={themed($fieldContainer)}
      />

      {/* Email Field */}
      <TextField
        labelTx="editProfileScreen:emailLabel"
        value={formData.email}
        onChangeText={(text) => onFieldChange("email", text)}
        placeholderTx="editProfileScreen:enterYourEmailPlaceholder"
        keyboardType="email-address"
        autoCapitalize="none"
        autoComplete="email"
        helper={errors.email}
        status={errors.email ? "error" : undefined}
        containerStyle={themed($fieldContainer)}
      />

      {/* Phone Fields */}
      <Text tx="editProfileScreen:phoneLabel" style={themed($fieldLabel)} size="sm" weight="medium" />
      <View style={themed($phoneContainer)}>
        <TouchableOpacity
          style={themed($countrySelector)}
          onPress={() => {
            Keyboard.dismiss()
            setShowCountryModal(true)
          }}
        >
          <Text 
            text={currentCountry?.flag || "🌍"} 
            style={themed($flagText)} 
            size="md" 
          />
          <Text 
            text={displayCountryCode} 
            style={themed($countryText)} 
            size="sm" 
          />
          <Icon icon="caretRight" size={16} />
        </TouchableOpacity>

        <TextField
          value={formData.phone}
          onChangeText={(text) => onFieldChange("phone", text)}
          placeholderTx="editProfileScreen:phonePlaceholder"
          keyboardType="phone-pad"
          status={errors.phone ? "error" : undefined}
          containerStyle={themed($phoneField)}
        />
      </View>
      {errors.phone && (
        <Text text={errors.phone} style={themed($errorText)} size="xs" />
      )}

      {/* Gender Field */}
      <Text tx="editProfileScreen:genderLabel" style={themed($fieldLabel)} size="sm" weight="medium" />
      <TouchableOpacity
        style={themed($selector)}
        onPress={() => {
          Keyboard.dismiss()
          setShowGenderModal(true)
        }}
      >
        <Text 
          text={currentGender?.label || formData.gender} 
          style={themed($selectorText)} 
          size="md" 
        />
        <Icon icon="caretRight" size={16} />
      </TouchableOpacity>
      {errors.gender && (
        <Text text={errors.gender} style={themed($errorText)} size="xs" />
      )}

      {/* Date of Birth Field */}
      <Text tx="editProfileScreen:dateOfBirthLabel" style={themed($fieldLabel)} size="sm" weight="medium" />
      <TouchableOpacity 
        style={themed($selector)} 
        onPress={handleOpenDatePicker}
      >
        <Icon icon="view" size={20} />
        <Text 
          text={formatDateForDisplay(formData.dateOfBirth)} 
          style={themed($selectorText)} 
          size="md" 
        />
        <Icon icon="caretRight" size={16} />
      </TouchableOpacity>
      {errors.dateOfBirth && (
        <Text text={errors.dateOfBirth} style={themed($errorText)} size="xs" />
      )}

      {/* Description Field */}
      <TextField
        labelTx="editProfileScreen:descriptionLabel"
        value={formData.description}
        onChangeText={(text) => onFieldChange("description", text)}
        placeholderTx="editProfileScreen:descriptionPlaceholder"
        multiline
        helperTx={errors.description ? undefined : "editProfileScreen:characterCount"}
        helperTxOptions={errors.description ? undefined : { count: formData.description.length }}
        helper={errors.description}
        status={errors.description ? "error" : undefined}
        containerStyle={themed($fieldContainer)}
      />

      {/* Location Field */}
      <TextField
        labelTx="editProfileScreen:locationLabel"
        value={formData.location}
        onChangeText={(text) => onFieldChange("location", text)}
        placeholderTx="editProfileScreen:locationPlaceholder"
        helper={errors.location}
        status={errors.location ? "error" : undefined}
        containerStyle={themed($fieldContainer)}
      />

      {/* Selection Modals */}
      <SelectionSheet
        visible={showCountryModal}
        onClose={() => setShowCountryModal(false)}
        onSelect={(value) => onFieldChange("countryCode", value)}
        items={COUNTRY_CODES.map(country => ({
          value: country.code,
          label: country.label,
          flag: country.flag,
        }))}
        title={translate("editProfileScreen:selectCountryTitle")}
        selectedValue={formData.countryCode}
      />

      <SelectionSheet
        visible={showGenderModal}
        onClose={() => setShowGenderModal(false)}
        onSelect={(value) => onFieldChange("gender", value)}
        items={GENDER_OPTIONS}
        title={translate("editProfileScreen:selectGenderTitle")}
        selectedValue={formData.gender}
      />

      {/* Date Picker Modal (iOS only) */}
      {Platform.OS === "ios" && (
        <DatePickerModal
          visible={showDatePicker}
          onClose={() => setShowDatePicker(false)}
          onSelect={handleDateSelect}
          value={formData.dateOfBirth}
          title={translate("editProfileScreen:selectDateTitle")}
          maximumDate={new Date()} // Can't be born in the future
          minimumDate={new Date(1900, 0, 1)} // Reasonable minimum date
        />
      )}
    </View>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
})

const $sectionTitle: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  color: colors.text,
  marginBottom: spacing.lg,
})

const $fieldContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

const $fieldLabel: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginBottom: spacing.xs,
})

const $phoneContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  gap: spacing.sm,
  marginBottom: spacing.xs,
})

const $countrySelector: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral200,
  borderWidth: 1,
  borderColor: colors.palette.neutral400,
  borderRadius: 4,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  gap: spacing.xs,
  minWidth: 120,
  minHeight: 40,
})

const $flagText: ThemedStyle<ViewStyle> = () => ({
  minWidth: 20,
})

const $countryText: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
})

const $phoneField: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $selector: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral200,
  borderWidth: 1,
  borderColor: colors.palette.neutral400,
  borderRadius: 4,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.md,
  marginBottom: spacing.xs,
  gap: spacing.sm,
})

const $selectorText: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
})

const $errorText: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  color: colors.error,
  marginBottom: spacing.sm,
})

const $iconColor: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
}) 