import { observer } from "mobx-react-lite"
import { FC } from "react"
import { ScrollView, ViewStyle } from "react-native"
import { Screen, UpcomingMatch } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { $styles } from "@/theme"
import { getSafeAreaConfig } from "@/utils/safeAreaHelpers"
import { useAppTheme } from "@/utils/useAppTheme"

import { useMainData } from "./hooks/useMainData"
import { useMainNavigation } from "./hooks/useMainNavigation"
import { useMainScrollToTop } from "./hooks/useMainScrollToTop"
import { useProfileValidation } from "./hooks/useProfileValidation"
import { 
  MainHeader, 
  UpcomingMatches, 
  Explore, 
  TrendingCommunities, 
  Events 
} from "./components"
import { scrollManager } from "./utils"
import type { ThemedStyle } from "@/theme"

export const MainScreen: FC<AppStackScreenProps<"Main">> = observer(function MainScreen(props) {
  const {
    theme: { colors },
    themed,
  } = useAppTheme()

  // Custom hooks for data, actions, and scroll management
  const { trendingCommunities, featuredEvents } = useMainData()

  const {
    handleAvatarPress,
    handleNotificationPress,
    handleInboxPress,
    handleLogoutPress,
    handleExploreCommunities,
    handleExploreEvents,
    handleExploreFeed,
    handleCommunityPress,
    handleEventPress,
  } = useMainNavigation({
    navigation: props.navigation,
  })

  const { scrollViewRef } = useMainScrollToTop()

  // Check for mandatory profile fields and navigate to EditProfile if needed
  useProfileValidation({ navigation: props.navigation })

  // Get random upcoming matches from events data
  const upcomingMatches: UpcomingMatch[] = (() => {
    const eventsData = require("@/data/events.json")
    // Randomly select 2 events and convert to UpcomingMatch format
    const shuffled = [...eventsData].sort(() => 0.5 - Math.random())
    return shuffled.slice(0, 2).map((event) => ({
      id: event.id,
      title: event.title,
      date: event.date,
      location: event.location,
      status: "Registered",
    }))
  })()

  // Handle upcoming match press
  const handleMatchPress = (match: UpcomingMatch) => {
    // Navigate to EventDetail using the updated navigation
    props.navigation.navigate("EventDetail", { eventId: match.id })
  }

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={getSafeAreaConfig("NONE")}
      contentContainerStyle={$styles.flex1}
      backgroundColor={colors.background}
    >
      {/* Main Header */}
      <MainHeader 
        onAvatarPress={handleAvatarPress} 
        onNotificationPress={handleNotificationPress} 
        onInboxPress={handleInboxPress}
        onLogoutPress={handleLogoutPress}
      />

      {/* Main Content Sections */}
      <ScrollView
        ref={scrollViewRef}
        style={themed($scrollView)}
        showsVerticalScrollIndicator={false}
      >
        {/* Upcoming Matches Section */}
        <UpcomingMatches 
          upcomingMatches={upcomingMatches}
          onMatchPress={handleMatchPress}
        />

        {/* Explore Section */}
        <Explore
          onExploreCommunities={handleExploreCommunities}
          onExploreEvents={handleExploreEvents}
          onExploreFeed={handleExploreFeed}
        />

        {/* Trending Communities Section */}
        <TrendingCommunities
          trendingCommunities={trendingCommunities}
          onCommunityPress={handleCommunityPress}
        />

        {/* Events Section */}
        <Events
          featuredEvents={featuredEvents}
          onEventPress={handleEventPress}
        />
      </ScrollView>
    </Screen>
  )
})

// Export the scroll function for use in navigator
export const triggerMainScreenScrollToTop = () => {
  scrollManager.triggerScrollToTop()
}

const $scrollView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})
